[2025-05-15 07:43:29] production.ERROR: Command "server" is not defined.

Did you mean one of these?
    make:observer
    serve {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"server\" is not defined.

Did you mean one of these?
    make:observer
    serve at D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php:720)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('server')
#1 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-05-15 07:44:29] production.ERROR: could not find driver (SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(377): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(86): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(46): App\\Http\\Controllers\\Auth\\LoginController->attemptLogin(Object(Illuminate\\Http\\Request))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\AHCRepos\\doctors_portal\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#60 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=aws-...', 'postgres.twtall...', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=aws-...', 'postgres.twtall...', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=aws-...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(377): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(86): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(46): App\\Http\\Controllers\\Auth\\LoginController->attemptLogin(Object(Illuminate\\Http\\Request))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\AHCRepos\\doctors_portal\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#70 {main}
"} 
[2025-05-15 07:44:38] production.ERROR: could not find driver (SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(377): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(86): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(46): App\\Http\\Controllers\\Auth\\LoginController->attemptLogin(Object(Illuminate\\Http\\Request))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\AHCRepos\\doctors_portal\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#60 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=aws-...', 'postgres.twtall...', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=aws-...', 'postgres.twtall...', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=aws-...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(377): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(86): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(46): App\\Http\\Controllers\\Auth\\LoginController->attemptLogin(Object(Illuminate\\Http\\Request))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\AHCRepos\\doctors_portal\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#70 {main}
"} 
[2025-05-15 08:45:34] production.ERROR: could not find driver (SQL: select * from "users" where "email" = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from \"users\" where \"email\" = <EMAIL> limit 1) at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(377): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(86): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(46): App\\Http\\Controllers\\Auth\\LoginController->attemptLogin(Object(Illuminate\\Http\\Request))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\AHCRepos\\doctors_portal\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#60 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(134): Illuminate\\Database\\Eloquent\\Builder->first()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(377): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(86): Illuminate\\Auth\\SessionGuard->attempt(Array, false)
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php(46): App\\Http\\Controllers\\Auth\\LoginController->attemptLogin(Object(Illuminate\\Http\\Request))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Auth\\LoginController->login(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('login', Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Auth\\LoginController), 'login')
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\AHCRepos\\doctors_portal\\app\\Http\\Middleware\\RedirectIfAuthenticated.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\RedirectIfAuthenticated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#70 {main}
"} 
[2025-05-15 08:46:32] production.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-05-15 08:47:30] dev.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-05-15 08:47:58] dev.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#14 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#26 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#24 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#36 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 {main}
"} 
[2025-05-15 08:48:08] dev.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-05-15 08:48:54] dev.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#14 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#24 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\StatusCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 {main}
"} 
[2025-05-15 08:54:31] dev.ERROR: could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}
"} 
[2025-05-15 08:58:15] dev.ERROR: SQLSTATE[08006] [7] could not translate host name "db.braymuprxgppvihegkot.supabase.co" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(49): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-05-15 08:59:21] dev.ERROR: SQLSTATE[08006] [7] could not translate host name "db.braymuprxgppvihegkot.supabase.co" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(49): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-05-15 09:02:23] dev.ERROR: SQLSTATE[08006] [7] could not translate host name "db.braymuprxgppvihegkot.supabase.co" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(49): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-05-15 09:02:45] dev.ERROR: SQLSTATE[08006] [7] could not translate host name "https://db.braymuprxgppvihegkot.supabase.co" to address: Unknown host (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] could not translate host name \"https://db.braymuprxgppvihegkot.supabase.co\" to address: Unknown host (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] could not translate host name \"https://db.braymuprxgppvihegkot.supabase.co\" to address: Unknown host at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=http...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=http...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(49): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'pgsql:host=http...', 'postgres', 'Roshan@2024@', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=http...', Array, Array)
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-05-15 09:04:13] dev.ERROR: SQLSTATE[08006] [7] could not translate host name "db.braymuprxgppvihegkot.supabase.co" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error (SQL: select * from information_schema.tables where table_schema = public and table_name = migrations and table_type = 'BASE TABLE') at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 7): SQLSTATE[08006] [7] could not translate host name \"db.braymuprxgppvihegkot.supabase.co\" to address: Unknown server error at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('pgsql:host=db.b...', 'postgres', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(100): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(49): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'pgsql:host=db.b...', 'postgres', 'Roshan@2024@', Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\PostgresConnector.php(32): Illuminate\\Database\\Connectors\\Connector->createConnection('pgsql:host=db.b...', Array, Array)
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\PostgresConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from i...', Array, Object(Closure))
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\PostgresBuilder.php(45): Illuminate\\Database\\Connection->select('select * from i...', Array)
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(169): Illuminate\\Database\\Schema\\PostgresBuilder->hasTable('migrations')
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(673): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(78): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(606): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(77): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(136): Illuminate\\Container\\Container->call(Array)
#28 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Command\\Command.php(298): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(120): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(1040): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-05-15 10:37:26] dev.ERROR: Command "seed" is not defined.

Did you mean one of these?
    db:seed
    make:seeder {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"seed\" is not defined.

Did you mean one of these?
    db:seed
    make:seeder at D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php:720)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(259): Symfony\\Component\\Console\\Application->find('seed')
#1 D:\\AHCRepos\\doctors_portal\\vendor\\symfony\\console\\Application.php(171): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(94): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(129): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\AHCRepos\\doctors_portal\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-05-15 10:38:42] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:38:42] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available"]],"kwargs":{"context":{"lang":"en_US"}}},"id":527537415}} 
[2025-05-15 10:39:24] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:39:24] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","pana"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":468951937}} 
[2025-05-15 10:39:36] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:39:36] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","panad"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":61379572}} 
[2025-05-15 10:39:49] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:39:49] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","anti"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":373462202}} 
[2025-05-15 10:40:38] dev.INFO: Query results: {"count":21,"total":21,"items":[{"App\\Models\\Patient":{"id":15,"first_name":"Orie","last_name":"Barton","date_of_birth":"2015-06-24T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"434 Bogisich Circle
North Arnoburgh, KY 23361-5533","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":21,"first_name":"Brianne","last_name":"Block","date_of_birth":"1985-05-04T00:00:00.000000Z","email":"<EMAIL>","phone":"+12403193074","address":"6745 Moore Stravenue Apt. 369
West Rahul, ND 48370","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":7,"first_name":"Malachi","last_name":"Casper","date_of_birth":"1990-10-09T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"7971 Gleichner Lodge
Osinskistad, OH 34313-7926","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":1,"first_name":"John","last_name":"Doe","date_of_birth":"1990-01-01T00:00:00.000000Z","email":"<EMAIL>","phone":"**********","address":"123 Main St","created_at":"2025-05-15T10:37:33.000000Z","updated_at":"2025-05-15T10:37:33.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":20,"first_name":"Gerson","last_name":"Gottlieb","date_of_birth":"1985-02-22T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"456 Kasandra Corners Apt. 908
Marquardtbury, NH 64735-9116","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":4,"first_name":"Eduardo","last_name":"Hane","date_of_birth":"2010-05-30T00:00:00.000000Z","email":"<EMAIL>","phone":"+****************","address":"7955 Marjorie Villages Apt. 808
Lake Mariannashire, ID 37412-3342","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":2,"first_name":"Gilda","last_name":"Hill","date_of_birth":"1985-08-21T00:00:00.000000Z","email":"<EMAIL>","phone":"******.801.1090","address":"3166 Gonzalo Dam Apt. 016
Port Noraside, AR 97614-2472","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":3,"first_name":"Richard","last_name":"Howell","date_of_birth":"2003-09-29T00:00:00.000000Z","email":"<EMAIL>","phone":"1-************","address":"131 Erdman Road
Mertieville, AZ 29356-3279","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":14,"first_name":"Kirstin","last_name":"Jones","date_of_birth":"1998-01-30T00:00:00.000000Z","email":"<EMAIL>","phone":"(*************","address":"5222 Hills Lake Apt. 221
Hellermouth, NH 78419-5358","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":11,"first_name":"Alfonso","last_name":"Koss","date_of_birth":"1974-07-18T00:00:00.000000Z","email":"<EMAIL>","phone":"******.216.6022","address":"21007 Marquardt Keys Apt. 458
Murrayburgh, OK 32311","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":5,"first_name":"Brook","last_name":"Little","date_of_birth":"2010-07-13T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"31556 Breitenberg Prairie Apt. 805
Lake Edyth, MD 70511-4589","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":6,"first_name":"Hazel","last_name":"Medhurst","date_of_birth":"1976-03-07T00:00:00.000000Z","email":"<EMAIL>","phone":"(*************","address":"8521 Casimir Mountains Suite 432
Austyntown, WI 79533-6755","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":17,"first_name":"Frida","last_name":"Nolan","date_of_birth":"1999-02-24T00:00:00.000000Z","email":"<EMAIL>","phone":"+1-************","address":"92264 Rosenbaum Inlet Suite 814
Stefanside, NV 83744","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":16,"first_name":"Keely","last_name":"Pollich","date_of_birth":"1984-12-29T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"9037 Danny Land
Lueilwitzmouth, SC 88021","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":10,"first_name":"Deonte","last_name":"Prosacco","date_of_birth":"1991-03-25T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"724 Bashirian Turnpike Apt. 922
Bertside, KS 11281","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":8,"first_name":"Rubye","last_name":"Rippin","date_of_birth":"1992-07-03T00:00:00.000000Z","email":"<EMAIL>","phone":"1-************","address":"807 Sadye Flat Suite 512
Derekstad, KS 33611","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":18,"first_name":"Jada","last_name":"Ryan","date_of_birth":"1996-11-19T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"54165 Bode Ranch
East Hallietown, CA 98867","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":13,"first_name":"Scottie","last_name":"Schaden","date_of_birth":"2001-10-11T00:00:00.000000Z","email":"<EMAIL>","phone":"+12256588231","address":"22915 Swift Viaduct
Mariannaburgh, MA 29676","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":19,"first_name":"Gilberto","last_name":"Towne","date_of_birth":"1991-01-10T00:00:00.000000Z","email":"<EMAIL>","phone":"******.947.8122","address":"1034 Cummings Neck
Conroyhaven, KS 61553-4109","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":9,"first_name":"Lessie","last_name":"Ward","date_of_birth":"2000-11-13T00:00:00.000000Z","email":"<EMAIL>","phone":"******.790.1973","address":"5742 Zulauf Parkway Suite 895
Stoltenbergmouth, MN 26998","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":12,"first_name":"Florencio","last_name":"Wehner","date_of_birth":"2013-03-05T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"2354 Jeanne Trail Apt. 321
North Madelyn, MD 00503","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}}]} 
[2025-05-15 10:41:28] dev.INFO: Query results: {"count":4,"total":4,"items":[{"App\\Models\\Patient":{"id":21,"first_name":"Brianne","last_name":"Block","date_of_birth":"1985-05-04T00:00:00.000000Z","email":"<EMAIL>","phone":"+12403193074","address":"6745 Moore Stravenue Apt. 369
West Rahul, ND 48370","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":20,"first_name":"Gerson","last_name":"Gottlieb","date_of_birth":"1985-02-22T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"456 Kasandra Corners Apt. 908
Marquardtbury, NH 64735-9116","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":2,"first_name":"Gilda","last_name":"Hill","date_of_birth":"1985-08-21T00:00:00.000000Z","email":"<EMAIL>","phone":"******.801.1090","address":"3166 Gonzalo Dam Apt. 016
Port Noraside, AR 97614-2472","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":18,"first_name":"Jada","last_name":"Ryan","date_of_birth":"1996-11-19T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"54165 Bode Ranch
East Hallietown, CA 98867","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-05-15T10:37:36.000000Z","odoo_partner_id":null,"sync_status":null,"sync_error":null,"last_synced_at":null}}]} 
[2025-05-15 10:41:54] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:41:54] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","panal"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":*********}} 
[2025-05-15 10:42:11] dev.INFO: Creating partner in Odoo {"data":{"name":"Brianne Block","phone":"+12403193074","mobile":false,"email":"<EMAIL>","customer_rank":1,"company_type":"person","type":"contact"}} 
[2025-05-15 10:42:13] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:42:13] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"res.partner","method":"create","args":[{"name":"Brianne Block","phone":"+12403193074","mobile":false,"email":"<EMAIL>","customer_rank":1,"company_type":"person","type":"contact"}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","tracking_disable":true}}},"id":*********}} 
[2025-05-15 10:42:13] dev.INFO: Partner created successfully {"partner_id":35988} 
[2025-05-15 10:42:13] dev.INFO: Creating sales order in Odoo {"data":{"partner_id":0,"date_order":"2025-05-15 10:42:13","state":"draft","company_id":1,"pricelist_id":1,"user_id":null,"doctor_id":0,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}} 
[2025-05-15 10:42:14] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-05-15 10:42:14] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"create","args":[{"partner_id":0,"date_order":"2025-05-15 10:42:13","state":"draft","company_id":1,"pricelist_id":1,"user_id":null,"doctor_id":0,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":null,"default_company_id":1}}},"id":*********}} 
[2025-05-15 10:42:14] dev.ERROR: Odoo API call failed {"endpoint":"/web/dataset/call_kw","error":"The operation cannot be completed:
- Create/update: a mandatory field is not set.
- Delete: another model requires the record being deleted. If possible, archive it instead.

Model: Sales Order (sale.order)
Field: Customer (partner_id)
","retry_count":0} 
[2025-05-15 10:42:14] dev.ERROR: Failed to create sales order {"error":"The operation cannot be completed:
- Create/update: a mandatory field is not set.
- Delete: another model requires the record being deleted. If possible, archive it instead.

Model: Sales Order (sale.order)
Field: Customer (partner_id)
","data":{"partner_id":0,"prescription_reference":1,"doctor_id":0,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}} 
[2025-05-15 10:42:14] dev.ERROR: Prescription creation failed {"error":"Failed to create sales order: The operation cannot be completed:
- Create/update: a mandatory field is not set.
- Delete: another model requires the record being deleted. If possible, archive it instead.

Model: Sales Order (sale.order)
Field: Customer (partner_id)
","data":{"prescription_date":"2025-05-15","patient_id":"21","medications":[{"type":"odoo","product_id":"65051","custom_name":null,"quantity":"1","dosage":"1","every":"2","period":"hours","directions":"test"}]}} 
[2025-06-27 18:31:47] dev.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `users` where `id` = 2 limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `users` where `id` = 2 limit 1) at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(69): Illuminate\\Database\\Eloquent\\Builder->first()
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(196): Illuminate\\Auth\\EloquentUserProvider->retrieveByToken('2', 'tFNxPDP7oHXjOyx...')
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(167): Illuminate\\Auth\\SessionGuard->userFromRecaller(Object(Illuminate\\Auth\\Recaller))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\SessionGuard->check()
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#51 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#52 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#53 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(69): Illuminate\\Database\\Eloquent\\Builder->first()
#20 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(196): Illuminate\\Auth\\EloquentUserProvider->retrieveByToken('2', 'tFNxPDP7oHXjOyx...')
#21 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(167): Illuminate\\Auth\\SessionGuard->userFromRecaller(Object(Illuminate\\Auth\\Recaller))
#22 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php(60): Illuminate\\Auth\\SessionGuard->user()
#23 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Auth\\SessionGuard->check()
#24 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(42): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#25 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\AHCRepos\\doctors_portal\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 D:\\AHCRepos\\doctors_portal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 D:\\AHCRepos\\doctors_portal\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 D:\\AHCRepos\\doctors_portal\\server.php(21): require_once('D:\\\\AHCRepos\\\\doc...')
#63 {main}
"} 
