[2025-07-02 13:19:40] dev.INFO: Authenticated with <PERSON><PERSON><PERSON> {"uid":6,"has_session":true} 
[2025-07-02 13:19:40] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","TEST"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":15056469}} 
[2025-07-02 13:19:43] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-07-02 13:19:43] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","TEST M"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":722993745}} 
[2025-07-02 13:19:44] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-07-02 13:19:44] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","TEST ME"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":343382117}} 
[2025-07-02 13:19:45] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-07-02 13:19:45] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","TEST MEdi"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":142068701}} 
[2025-07-02 13:19:55] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-07-02 13:19:55] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["id","=",65051]],["id","name","default_code","list_price"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-07-02 13:19:55] dev.INFO: Creating prescription sales order in Odoo {"data":{"partner_id":19961,"date_order":"2025-07-02 13:19:55","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"34323456","patient":"ABDUL LATHEF","patient_portal_id":2}} 
[2025-07-02 13:19:55] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"create","args":[{"partner_id":19961,"date_order":"2025-07-02 13:19:55","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"34323456","patient":"ABDUL LATHEF","patient_portal_id":2}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6,"default_company_id":1}}},"id":*********}} 
[2025-07-02 13:19:55] dev.INFO: Sales order created successfully {"sale_order_id":114} 
[2025-07-02 13:19:55] dev.INFO: Adding order line to sales order {"order_id":114,"line_data":{"order_id":114,"product_id":65051,"name":"PANADOL ADVANCE 500MG 48 TABLETS","product_uom_qty":1,"price_unit":0.91,"product_uom":1,"tax_id":[[6,0,[]]]}} 
[2025-07-02 13:19:55] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order.line","method":"create","args":[{"order_id":114,"product_id":65051,"name":"PANADOL ADVANCE 500MG 48 TABLETS","product_uom_qty":1,"price_unit":0.91,"product_uom":1,"tax_id":[[6,0,[]]]}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6}}},"id":*********}} 
[2025-07-02 13:19:55] dev.INFO: Order line added successfully {"order_id":114,"line_id":120} 
[2025-07-02 13:19:56] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"read","args":[[114]],"kwargs":{"fields":["name","date_order","partner_id","doctor_id","patient_phone","patient","amount_total","state","order_line"]}},"id":*********}} 
[2025-07-02 13:20:31] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-07-02 13:20:31] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"search_read","args":[[["id","in",["110","111","112","114"]]],["id","name","state","date_order","amount_total","invoice_status"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-07-02 13:20:31] dev.INFO: Updated order status {"prescription_id":32,"odoo_order_id":"114","old_status":"Pending","new_status":"Draft","update_result":true} 
[2025-07-02 13:24:33] dev.INFO: Query results: {"count":6,"total":6,"items":[{"App\\Models\\Patient":{"id":20,"first_name":"ABBAS","last_name":"AE","date_of_birth":"1985-02-22T00:00:00.000000Z","email":"<EMAIL>","phone":"************","address":"456 Kasandra Corners Apt. 908

Marquardtbury, NH 64735-9116","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:53:04.000000Z","odoo_partner_id":19940,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":23,"first_name":"ABDUL","last_name":"AMIR","date_of_birth":"2020-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"54323456","address":"Test address","created_at":"2025-06-28T13:10:40.000000Z","updated_at":"2025-07-02T12:54:15.000000Z","odoo_partner_id":19953,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T13:16:36.000000Z"}},{"App\\Models\\Patient":{"id":22,"first_name":"ABDUL","last_name":"HUSSAIN","date_of_birth":"2010-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"+96550071076","address":"This is a test address","created_at":"2025-06-28T10:58:45.000000Z","updated_at":"2025-07-02T12:55:07.000000Z","odoo_partner_id":19960,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T20:14:31.000000Z"}},{"App\\Models\\Patient":{"id":2,"first_name":"ABDUL","last_name":"LATHEF","date_of_birth":"1985-08-21T00:00:00.000000Z","email":"<EMAIL>","phone":"34323456","address":"3166 Gonzalo Dam Apt. 016

Port Noraside, AR 97614-2472","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:55:45.000000Z","odoo_partner_id":19961,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":18,"first_name":"ABDUL","last_name":"RAHMAN HA","date_of_birth":"1996-11-19T00:00:00.000000Z","email":"<EMAIL>","phone":"99011448","address":"54165 Bode Ranch

East Hallietown, CA 98867","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:56:30.000000Z","odoo_partner_id":19965,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":21,"first_name":"ABBRONZANTE","last_name":"SUBLIME","date_of_birth":"1985-05-04T00:00:00.000000Z","email":"<EMAIL>","phone":"+12403193074","address":"6745 Moore Stravenue Apt. 369

West Rahul, ND 48370","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:53:41.000000Z","odoo_partner_id":19943,"sync_status":null,"sync_error":null,"last_synced_at":null}}]} 
[2025-07-02 13:24:45] dev.INFO: Query results: {"count":6,"total":6,"items":[{"App\\Models\\Patient":{"id":20,"first_name":"ABBAS","last_name":"AE","date_of_birth":"1985-02-22T00:00:00.000000Z","email":"<EMAIL>","phone":"**********","address":"456 Kasandra Corners Apt. 908

Marquardtbury, NH 64735-9116","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T13:24:45.000000Z","odoo_partner_id":19940,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":23,"first_name":"ABDUL","last_name":"AMIR","date_of_birth":"2020-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"54323456","address":"Test address","created_at":"2025-06-28T13:10:40.000000Z","updated_at":"2025-07-02T12:54:15.000000Z","odoo_partner_id":19953,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T13:16:36.000000Z"}},{"App\\Models\\Patient":{"id":22,"first_name":"ABDUL","last_name":"HUSSAIN","date_of_birth":"2010-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"+96550071076","address":"This is a test address","created_at":"2025-06-28T10:58:45.000000Z","updated_at":"2025-07-02T12:55:07.000000Z","odoo_partner_id":19960,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T20:14:31.000000Z"}},{"App\\Models\\Patient":{"id":2,"first_name":"ABDUL","last_name":"LATHEF","date_of_birth":"1985-08-21T00:00:00.000000Z","email":"<EMAIL>","phone":"34323456","address":"3166 Gonzalo Dam Apt. 016

Port Noraside, AR 97614-2472","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:55:45.000000Z","odoo_partner_id":19961,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":18,"first_name":"ABDUL","last_name":"RAHMAN HA","date_of_birth":"1996-11-19T00:00:00.000000Z","email":"<EMAIL>","phone":"99011448","address":"54165 Bode Ranch

East Hallietown, CA 98867","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:56:30.000000Z","odoo_partner_id":19965,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":21,"first_name":"ABBRONZANTE","last_name":"SUBLIME","date_of_birth":"1985-05-04T00:00:00.000000Z","email":"<EMAIL>","phone":"+12403193074","address":"6745 Moore Stravenue Apt. 369

West Rahul, ND 48370","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:53:41.000000Z","odoo_partner_id":19943,"sync_status":null,"sync_error":null,"last_synced_at":null}}]} 
[2025-07-02 13:24:54] dev.INFO: Query results: {"count":6,"total":6,"items":[{"App\\Models\\Patient":{"id":20,"first_name":"ABBAS","last_name":"AE","date_of_birth":"1985-02-22T00:00:00.000000Z","email":"<EMAIL>","phone":"**********","address":"456 Kasandra Corners Apt. 908

Marquardtbury, NH 64735-9116","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T13:24:45.000000Z","odoo_partner_id":19940,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":23,"first_name":"ABDUL","last_name":"AMIR","date_of_birth":"2020-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"54323456","address":"Test address","created_at":"2025-06-28T13:10:40.000000Z","updated_at":"2025-07-02T12:54:15.000000Z","odoo_partner_id":19953,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T13:16:36.000000Z"}},{"App\\Models\\Patient":{"id":22,"first_name":"ABDUL","last_name":"HUSSAIN","date_of_birth":"2010-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"96550071076","address":"This is a test address","created_at":"2025-06-28T10:58:45.000000Z","updated_at":"2025-07-02T13:24:54.000000Z","odoo_partner_id":19960,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T20:14:31.000000Z"}},{"App\\Models\\Patient":{"id":2,"first_name":"ABDUL","last_name":"LATHEF","date_of_birth":"1985-08-21T00:00:00.000000Z","email":"<EMAIL>","phone":"34323456","address":"3166 Gonzalo Dam Apt. 016

Port Noraside, AR 97614-2472","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:55:45.000000Z","odoo_partner_id":19961,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":18,"first_name":"ABDUL","last_name":"RAHMAN HA","date_of_birth":"1996-11-19T00:00:00.000000Z","email":"<EMAIL>","phone":"99011448","address":"54165 Bode Ranch

East Hallietown, CA 98867","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:56:30.000000Z","odoo_partner_id":19965,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":21,"first_name":"ABBRONZANTE","last_name":"SUBLIME","date_of_birth":"1985-05-04T00:00:00.000000Z","email":"<EMAIL>","phone":"+12403193074","address":"6745 Moore Stravenue Apt. 369

West Rahul, ND 48370","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:53:41.000000Z","odoo_partner_id":19943,"sync_status":null,"sync_error":null,"last_synced_at":null}}]} 
[2025-07-02 13:25:04] dev.INFO: Query results: {"count":6,"total":6,"items":[{"App\\Models\\Patient":{"id":20,"first_name":"ABBAS","last_name":"AE","date_of_birth":"1985-02-22T00:00:00.000000Z","email":"<EMAIL>","phone":"**********","address":"456 Kasandra Corners Apt. 908

Marquardtbury, NH 64735-9116","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T13:24:45.000000Z","odoo_partner_id":19940,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":23,"first_name":"ABDUL","last_name":"AMIR","date_of_birth":"2020-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"54323456","address":"Test address","created_at":"2025-06-28T13:10:40.000000Z","updated_at":"2025-07-02T12:54:15.000000Z","odoo_partner_id":19953,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T13:16:36.000000Z"}},{"App\\Models\\Patient":{"id":22,"first_name":"ABDUL","last_name":"HUSSAIN","date_of_birth":"2010-01-28T00:00:00.000000Z","email":"<EMAIL>","phone":"96550071076","address":"This is a test address","created_at":"2025-06-28T10:58:45.000000Z","updated_at":"2025-07-02T13:24:54.000000Z","odoo_partner_id":19960,"sync_status":"synced","sync_error":null,"last_synced_at":"2025-06-28T20:14:31.000000Z"}},{"App\\Models\\Patient":{"id":2,"first_name":"ABDUL","last_name":"LATHEF","date_of_birth":"1985-08-21T00:00:00.000000Z","email":"<EMAIL>","phone":"34323456","address":"3166 Gonzalo Dam Apt. 016

Port Noraside, AR 97614-2472","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:55:45.000000Z","odoo_partner_id":19961,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":18,"first_name":"ABDUL","last_name":"RAHMAN HA","date_of_birth":"1996-11-19T00:00:00.000000Z","email":"<EMAIL>","phone":"99011448","address":"54165 Bode Ranch

East Hallietown, CA 98867","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T12:56:30.000000Z","odoo_partner_id":19965,"sync_status":null,"sync_error":null,"last_synced_at":null}},{"App\\Models\\Patient":{"id":21,"first_name":"ABBRONZANTE","last_name":"SUBLIME","date_of_birth":"1985-05-04T00:00:00.000000Z","email":"<EMAIL>","phone":"403193074","address":"6745 Moore Stravenue Apt. 369

West Rahul, ND 48370","created_at":"2025-05-15T10:37:36.000000Z","updated_at":"2025-07-02T13:25:04.000000Z","odoo_partner_id":19943,"sync_status":null,"sync_error":null,"last_synced_at":null}}]} 
