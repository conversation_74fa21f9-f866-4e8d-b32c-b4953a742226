[2025-06-29 07:32:52] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-06-29 07:32:52] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["id","=",65050]],["id","name","default_code","list_price"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-06-29 07:32:52] dev.INFO: Creating prescription sales order in Odoo {"data":{"partner_id":19938,"date_order":"2025-06-29 07:32:52","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brian<PERSON> Block","patient_portal_id":21}} 
[2025-06-29 07:32:52] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"create","args":[{"partner_id":19938,"date_order":"2025-06-29 07:32:52","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6,"default_company_id":1}}},"id":*********}} 
[2025-06-29 07:32:52] dev.INFO: Sales order created successfully {"sale_order_id":106} 
[2025-06-29 07:32:52] dev.INFO: Adding order line to sales order {"order_id":106,"line_data":{"order_id":106,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":1,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}} 
[2025-06-29 07:32:52] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order.line","method":"create","args":[{"order_id":106,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":1,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6}}},"id":*********}} 
[2025-06-29 07:32:52] dev.INFO: Order line added successfully {"order_id":106,"line_id":113} 
[2025-06-29 07:32:53] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"read","args":[[106]],"kwargs":{"fields":["name","date_order","partner_id","doctor_id","patient_phone","patient","amount_total","state","order_line"]}},"id":*********}} 
[2025-06-29 07:37:20] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-06-29 07:37:20] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["id","=",65050]],["id","name","default_code","list_price"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-06-29 07:37:20] dev.INFO: Creating prescription sales order in Odoo {"data":{"partner_id":19938,"date_order":"2025-06-29 07:37:20","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}} 
[2025-06-29 07:37:20] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"create","args":[{"partner_id":19938,"date_order":"2025-06-29 07:37:20","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6,"default_company_id":1}}},"id":*********}} 
[2025-06-29 07:37:20] dev.INFO: Sales order created successfully {"sale_order_id":107} 
[2025-06-29 07:37:20] dev.INFO: Adding order line to sales order {"order_id":107,"line_data":{"order_id":107,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":1,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}} 
[2025-06-29 07:37:20] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order.line","method":"create","args":[{"order_id":107,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":1,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6}}},"id":*********}} 
[2025-06-29 07:37:20] dev.INFO: Order line added successfully {"order_id":107,"line_id":114} 
[2025-06-29 07:37:21] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"read","args":[[107]],"kwargs":{"fields":["name","date_order","partner_id","doctor_id","patient_phone","patient","amount_total","state","order_line"]}},"id":*********}} 
[2025-06-29 07:37:22] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-06-29 07:37:22] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-06-29 07:39:35] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-06-29 07:39:35] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["name","ilike","pana"],["type","=","product"],["sale_ok","=",true],["active","=",true]],["id","name","default_code","list_price","qty_available","uom_id"]],"kwargs":{"limit":10,"context":{"lang":"en_US"}}},"id":942391020}} 
[2025-06-29 07:39:45] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-06-29 07:39:45] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["id","=",65050]],["id","name","default_code","list_price"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-06-29 07:39:46] dev.INFO: Creating prescription sales order in Odoo {"data":{"partner_id":19938,"date_order":"2025-06-29 07:39:46","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}} 
[2025-06-29 07:39:46] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"create","args":[{"partner_id":19938,"date_order":"2025-06-29 07:39:46","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6,"default_company_id":1}}},"id":*********}} 
[2025-06-29 07:39:46] dev.INFO: Sales order created successfully {"sale_order_id":108} 
[2025-06-29 07:39:46] dev.INFO: Adding order line to sales order {"order_id":108,"line_data":{"order_id":108,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":2,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}} 
[2025-06-29 07:39:46] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order.line","method":"create","args":[{"order_id":108,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":2,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6}}},"id":*********}} 
[2025-06-29 07:39:46] dev.INFO: Order line added successfully {"order_id":108,"line_id":115} 
[2025-06-29 07:39:47] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"read","args":[[108]],"kwargs":{"fields":["name","date_order","partner_id","doctor_id","patient_phone","patient","amount_total","state","order_line"]}},"id":*********}} 
[2025-06-29 07:44:32] dev.INFO: Authenticated with Odoo {"uid":6,"has_session":true} 
[2025-06-29 07:44:32] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"product.product","method":"search_read","args":[[["id","=",65050]],["id","name","default_code","list_price"]],"kwargs":{"context":{"lang":"en_US"}}},"id":*********}} 
[2025-06-29 07:44:32] dev.INFO: Creating prescription sales order in Odoo {"data":{"partner_id":19938,"date_order":"2025-06-29 07:44:32","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}} 
[2025-06-29 07:44:32] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"create","args":[{"partner_id":19938,"date_order":"2025-06-29 07:44:32","state":"draft","company_id":1,"pricelist_id":1,"user_id":6,"doctor_id":23289,"patient_phone":"+12403193074","patient":"Brianne Block","patient_portal_id":21}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6,"default_company_id":1}}},"id":8892787}} 
[2025-06-29 07:44:33] dev.INFO: Sales order created successfully {"sale_order_id":109} 
[2025-06-29 07:44:33] dev.INFO: Adding order line to sales order {"order_id":109,"line_data":{"order_id":109,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":1,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}} 
[2025-06-29 07:44:33] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order.line","method":"create","args":[{"order_id":109,"product_id":65050,"name":"PANA NATURAL COUGH SYRUP 128G","product_uom_qty":1,"price_unit":2.75,"product_uom":1,"tax_id":[[6,0,[]]]}],"kwargs":{"context":{"lang":"en_US","tz":"Asia/Riyadh","uid":6}}},"id":47765669}} 
[2025-06-29 07:44:33] dev.INFO: Order line added successfully {"order_id":109,"line_id":116} 
[2025-06-29 07:44:34] dev.INFO: Odoo API Request {"endpoint":"/web/dataset/call_kw","data":{"jsonrpc":"2.0","method":"call","params":{"model":"sale.order","method":"read","args":[[109]],"kwargs":{"fields":["name","date_order","partner_id","doctor_id","patient_phone","patient","amount_total","state","order_line"]}},"id":*********}} 
