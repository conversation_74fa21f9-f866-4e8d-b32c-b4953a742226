

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Prescriptions</h2>
        <a href="<?php echo e(route('prescriptions.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Prescription
        </a>
    </div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form action="<?php echo e(route('prescriptions.index')); ?>" method="GET" class="row g-3">
            <div class="col-md-3">
                <input type="text" name="search" class="form-control" 
                       placeholder="Search patient name..." 
                       value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-3">
                <select name="sync_status" class="form-control">
                    <option value="">All Sync Status</option>
                    <option value="pending" <?php echo e(request('sync_status') == 'pending' ? 'selected' : ''); ?>>Pending</option>
                    <option value="synced" <?php echo e(request('sync_status') == 'synced' ? 'selected' : ''); ?>>Synced</option>
                    <option value="error" <?php echo e(request('sync_status') == 'error' ? 'selected' : ''); ?>>Error</option>
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" name="date" class="form-control" 
                       value="<?php echo e(request('date')); ?>"
                       placeholder="Filter by date">
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-secondary w-100">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>
</div>

    <?php if(session('warning')): ?>
        <div class="alert alert-warning">
            <?php echo e(session('warning')); ?>

        </div>
    <?php endif; ?>

    <!-- Prescriptions List -->
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Odoo ID</th>
                        <th>Date</th>
                        <th>Patient</th>
                        <th>Medications</th>
                        <th>Sync Status</th>
                        <th>Odoo Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $prescriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prescription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($prescription->id); ?></td>
                            <td>
                                <?php if($prescription->sync_status === 'synced' && $prescription->odoo_order_name): ?>
                                    <div>
                                        <strong><?php echo e($prescription->odoo_order_name); ?></strong>
                                        <br>
                                        <small class="text-muted">ID: <?php echo e($prescription->odoo_order_id); ?></small>
                                    </div>
                                <?php elseif($prescription->sync_status === 'pending'): ?>
                                    <span class="badge bg-warning text-dark">Pending Sync</span>
                                <?php elseif($prescription->sync_status === 'error'): ?>
                                    <span class="badge bg-danger">Sync Failed</span>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($prescription->prescription_date->format('M d, Y')); ?></td>
                            <td>
                                <a href="<?php echo e(route('patients.show', $prescription->patient)); ?>">
                                    <?php echo e($prescription->patient->first_name); ?> <?php echo e($prescription->patient->last_name); ?>

                                </a>
                            </td>
                            <td>
                                <?php $__currentLoopData = $prescription->medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mb-1">
                                        <?php if($medication->type === 'odoo'): ?>
                                            <?php echo e($medication->product_name ?? 'Unknown Product'); ?>

                                            <?php if($medication->product_code): ?>
                                                <small class="text-muted">(<?php echo e($medication->product_code); ?>)</small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?php echo e($medication->product); ?>

                                            <small class="text-muted">(Custom)</small>
                                        <?php endif; ?>
                                        <br>
                                        <small class="text-muted">
                                            Qty: <?php echo e($medication->quantity); ?> - 
                                            <?php echo e($medication->dosage); ?>

                                            <?php if($medication->every && $medication->period): ?>
                                                every <?php echo e($medication->every); ?> <?php echo e($medication->period); ?>

                                            <?php endif; ?>
                                            <?php if($medication->as_needed): ?>
                                                (as needed)
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </td>
                            <td>
                                <?php switch($prescription->sync_status):
                                    case ('synced'): ?>
                                        <span class="badge bg-success">Synced</span>
                                        <?php break; ?>
                                    <?php case ('error'): ?>
                                        <span class="badge bg-danger" 
                                              title="<?php echo e($prescription->sync_error); ?>">
                                            Error
                                        </span>
                                        <?php break; ?>
                                    <?php default: ?>
                                        <span class="badge bg-warning text-dark">Pending</span>
                                <?php endswitch; ?>
                            </td>
                            <td>
                                <?php switch($prescription->order_status):
                                    case ('synced'): ?>
                                        <span class="badge bg-success">Synced</span>
                                        <?php break; ?>
                                    <?php case ('error'): ?>
                                        <span class="badge bg-danger" 
                                              title="<?php echo e($prescription->sync_error); ?>">
                                            Error
                                        </span>
                                        <?php break; ?>
                                    <?php default: ?>
                                        <span class="badge bg-warning text-dark">Pending</span>
                                <?php endswitch; ?>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="<?php echo e(route('prescriptions.show', $prescription)); ?>" 
                                       class="btn btn-sm btn-info" 
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if($prescription->sync_status !== 'synced'): ?>
                                        <a href="<?php echo e(route('prescriptions.edit', $prescription)); ?>" 
                                           class="btn btn-sm btn-primary" 
                                           title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('prescriptions.destroy', $prescription)); ?>" 
                                              method="POST" 
                                              class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    class="btn btn-sm btn-danger" 
                                                    title="Delete"
                                                    onclick="return confirm('Are you sure you want to delete this prescription?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <?php if($prescription->sync_status === 'error'): ?>
                                        <button type="button" 
                                                class="btn btn-sm btn-warning" 
                                                title="Retry Sync"
                                                onclick="retrySync(<?php echo e($prescription->id); ?>)">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                No prescriptions found.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

    <!-- Pagination -->
    <div class="mt-4">
        <?php echo e($prescriptions->withQueryString()->links()); ?>

    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function retrySync(prescriptionId) {
    const button = $(event.target).closest('button');
    button.prop('disabled', true)
          .html('<i class="fas fa-spinner fa-spin"></i>');

    $.ajax({
        url: `/prescriptions/${prescriptionId}/resync`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Sync failed: ' + response.message);
                button.prop('disabled', false)
                      .html('<i class="fas fa-sync"></i>');
            }
        },
        error: function(xhr) {
            alert('Error occurred during sync. Please try again.');
            button.prop('disabled', false)
                  .html('<i class="fas fa-sync"></i>');
        }
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\AHCRepos\doctors_portal\resources\views/prescriptions/index.blade.php ENDPATH**/ ?>