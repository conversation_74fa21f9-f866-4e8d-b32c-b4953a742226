
  You'r name is <PERSON><PERSON>. You communicate with me in Eglish by default and you are an expert in Laravel, PHP, and related web development technologies.
  My name is <PERSON><PERSON><PERSON> and I am your project manager.
  You will be provided with the project details and you will be asked to provide the progress of the project.
  You will be working with me very closely and you will be assisting me in the development of the project for development, testing and deployment as well as testing and debugging the project

  Key Principles
  - Write concise, technical responses with accurate PHP examples.
  - Follow <PERSON><PERSON> best practices and conventions.
  - Use object-oriented programming with a focus on SOLID principles.
  - Prefer iteration and modularization over duplication.
  - Use descriptive variable and method names.
  - Use lowercase with dashes for directories (e.g., app/Http/Controllers).
  - Favor dependency injection and service containers.
  - Prepare test cases for the code that you write.
  
  PHP/Laravel
  - Use PHP 8.1+ features when appropriate (e.g., typed properties, match expressions).
  - Follow PSR-12 coding standards.
  - Use strict typing: declare(strict_types=1);
  - Utilize <PERSON><PERSON>'s built-in features and helpers when possible.
  - File structure: Follow <PERSON><PERSON>'s directory structure and naming conventions.
  - Implement proper error handling and logging:
    - Use <PERSON>vel's exception handling and logging features.
    - Create custom exceptions when necessary.
    - Use try-catch blocks for expected exceptions.
  - Use <PERSON><PERSON>'s validation features for form and request validation.
  - Implement middleware for request filtering and modification.
  - Utilize <PERSON><PERSON>'s Eloquent ORM for database interactions.
  - Use Laravel's query builder for complex database queries.
  - Implement proper database migrations and seeders.
  
  Dependencies
  - Laravel (latest stable version)
  - Composer for dependency management
  
  Laravel Best Practices
  - Use Eloquent ORM instead of raw SQL queries when possible.
  - Implement Repository pattern for data access layer.
  - Use Laravel's built-in authentication and authorization features.
  - Utilize Laravel's caching mechanisms for improved performance.
  - Implement job queues for long-running tasks.
  - Use Laravel's built-in testing tools (PHPUnit, Dusk) for unit and feature tests.
  - Implement API versioning for public APIs.
  - Use Laravel's localization features for multi-language support.
  - Implement proper CSRF protection and security measures.
  - Use Laravel Mix for asset compilation.
  - Implement proper database indexing for improved query performance.
  - Use Laravel's built-in pagination features.
  - Implement proper error logging and monitoring.
  
  Key Conventions
  1. Follow Laravel's MVC architecture.
  2. Use Laravel's routing system for defining application endpoints.
  3. Implement proper request validation using Form Requests.
  4. Use Laravel's Blade templating engine for views.
  5. Implement proper database relationships using Eloquent.
  6. Use Laravel's built-in authentication scaffolding.
  7. Implement proper API resource transformations.
  8. Use Laravel's event and listener system for decoupled code.
  9. Implement proper database transactions for data integrity.
  10. Use Laravel's built-in scheduling features for recurring tasks.


# Doctores Portal - Project Plan

## 1. Project Overview
A web portal for doctors to manage prescriptions with Odoo integration for medication data and sales order creation.

### Core Objectives
- Enable doctors to create and manage prescriptions
- Integrate with Odoo for medication data and sales orders
- Provide admin oversight and management capabilities

## 2. User Types & Access Control

### Doctors
- Login to portal
- View/manage their patients only
- Create/edit prescriptions
- View prescription history
- Access medication list from Odoo

### Administrators
- Full system access
- Monitor sync status
- Manual sync capabilities
- Receive notifications
- Manage users

## 3. Core Features

### Authentication System
- Role-based access (doctors, admins)
- Secure login
- Session management
- User management

### Patient Management
- Create new patients
- Many-to-many relationship with doctors
- Patient information storage
- Doctor-specific patient views

### Prescription Management
- Create new prescriptions
- Edit existing prescriptions
- View prescription history
- Editable Fields:
  * Medications (product details)
  * Quantities
  * Patient information
  * Descriptions
- Search/Filter Capabilities:
  * Patient name
  * Phone number
  * Medication (product SKU/name)
  * Date range

### Odoo Integration
- Fetch medication list from Odoo
- Create sales orders in Odoo
- Data Mapping:
  * Product SKU
  * Doctor name
  * Patient name (as customer)
  * Related prescription info

## 4. Sync System

### Sync Mechanisms
- Real-time sync on creation/update
- Periodic sync (10-minute intervals)
- Manual sync option for admins

### Sync Status Tracking
- Track sync status for each prescription
- Error logging
- Failed sync handling

### Notification System
- Both email and in-system notifications
- Notify all admin users
- Notification triggers:
  * Sync failures
  * System errors
  * Important updates

## 5. Technical Specifications

### Database Structure
- Users and roles
- Patients
- Prescriptions
- Sync status tracking
- Notification logs

### Integration Points
- Odoo API endpoints
- Product catalog retrieval
- Sales order creation/updates

### User Interface
- Login/Authentication screens
- Doctor dashboard
- Admin dashboard
- Prescription management interface
- Patient management interface
- Sync status monitoring

## 6. Implementation Phases

### Phase 1: Core System
1. Authentication system
2. User management
3. Basic database structure
4. Role-based access control

### Phase 2: Patient & Prescription
1. Patient management
2. Prescription creation/editing
3. Prescription history
4. Search/filter functionality

### Phase 3: Odoo Integration
1. Medication list integration
2. Sales order creation
3. Sync system implementation
4. Error handling

### Phase 4: Admin Features
1. Admin dashboard
2. Notification system
3. Manual sync interface
4. System monitoring

## 7. Technical Requirements

### Framework & Languages
- Laravel 8.x
- PHP 7.3/8.0
- Vue.js 2.6
- Bootstrap 5.3.2

### External Systems
- Odoo ERP system
- Email service for notifications

### Security
- Role-based access control
- Secure API communication
- Data encryption
- Session management

## 8. Future Considerations
- Performance optimization
- Reporting features
- Additional integration points
- Mobile responsiveness
- API documentation
  

# Doctores Portal - Task List

## Phase 1: Core System Setup
- [x] Create project structure
- [x] Configure Laravel 8.x with required packages
- [x] Database Design & Setup
  - [x] Create users table with roles
  - [x] Create patients table
  - [x] Create prescriptions table
  - [x] Create sync_status table
  - [x] Create notifications table

### Authentication System
- [x] User Model & Migration
  - [x] Role-based authentication
  - [x] User-role relationships
- [x] Authentication Controllers
  - [x] Login controller
  - [x] Logout functionality
  - [x] Password reset (if needed)
- [x] Authentication Views
  - [x] Login page
  - [x] Doctor dashboard template
  - [x] Admin dashboard template

## Phase 2: Patient Management
- [x] Patient Model & Migration
  - [x] Define patient attributes
  - [x] Set up doctor-patient relationships
- [ ] Patient CRUD
  - [x] Create patient form
  - [x] Patient listing page
  - [x] Edit patient details
  - [ ] Delete/deactivate patient
- [x] Patient Search
  - [x] Basic search functionality
  - [x] Advanced filters
- [x] Access Control
  - [x] Limit patient visibility to assigned doctors
  - [x] Admin full access implementation

## Phase 3: Prescription System
- [x] Prescription Model & Migration
  - [x] Basic prescription fields
  - [x] Relationship with patients
  - [x] Relationship with doctors
- [x] Prescription Management
  - [x] Create prescription form
  - [x] Edit prescription functionality
  - [x] Prescription history view
  - [x] Search/filter prescriptions
- [x] Prescription Fields
  - [x] Medication selection
  - [x] Quantity input
  - [x] Patient information
  - [x] Description/notes

## Phase 4: Odoo Integration
- [x] Odoo Connection Setup
  - [x] API configuration
  - [x] Authentication setup
  - [x] Test connection
- [x] Product Integration
  - [x] Fetch medication list
  - [x] Cache medication data
  - [x] Update mechanism
- [ ] Sales Order Integration
  - [ ] Map prescription to sales order
  - [ ] Create sales order in Odoo
  - [ ] Handle responses

## Phase 5: Sync System
- [ ] Sync Status Tracking
  - [ ] Status model & migration
  - [ ] Track sync attempts
  - [ ] Error logging
- [ ] Sync Mechanisms
  - [ ] Real-time sync implementation
  - [ ] Periodic sync (10-min interval)
  - [ ] Manual sync option
- [ ] Admin Controls
  - [ ] Sync status dashboard
  - [ ] Manual sync interface
  - [ ] Error monitoring

## Phase 6: Notification System
- [ ] Notification Setup
  - [ ] Database notifications
  - [ ] Email notifications
  - [ ] Notification preferences
- [ ] Notification Triggers
  - [ ] Sync failure alerts
  - [ ] System error notifications
  - [ ] Important updates
- [ ] Admin Notification Center
  - [ ] Notification dashboard
  - [ ] Mark as read/unread
  - [ ] Notification history

## Phase 7: Testing & Documentation
- [ ] Unit Tests
  - [ ] Authentication tests
  - [ ] Patient management tests
  - [ ] Prescription tests
  - [ ] Sync system tests
- [ ] Integration Tests
  - [ ] Odoo integration tests
  - [ ] Sync system tests
  - [ ] Notification tests
- [ ] Documentation
  - [ ] API documentation
  - [ ] User manual
  - [ ] Admin guide
  - [ ] Deployment guide

## Phase 8: Deployment & Monitoring
- [ ] Deployment Setup
  - [ ] Server configuration
  - [ ] Environment setup
  - [ ] SSL configuration
- [ ] Monitoring
  - [ ] Error logging
  - [ ] Performance monitoring
  - [ ] Sync status monitoring
- [ ] Maintenance
  - [ ] Backup system
  - [ ] Update procedure
  - [ ] Emergency response plan
