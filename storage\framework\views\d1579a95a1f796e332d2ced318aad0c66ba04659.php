

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title">Prescription Details</h3>
            <div>
                <span class="badge bg-<?php echo e($prescription->sync_status === 'synced' ? 'success' : ($prescription->sync_status === 'error' ? 'danger' : 'warning')); ?>">
                    <?php echo e(ucfirst($prescription->sync_status)); ?>

                </span>
                |
                <span class="badge bg-<?php echo e($prescription->order_status === 'Confirmed' ? 'success' : ($prescription->order_status === 'Cancelled' ? 'danger' : 'warning')); ?>">
                    Odoo:<?php echo e(ucfirst($prescription->order_status)); ?>

                </span>
            </div>
        </div>
        
        <div class="card-body">
            <!-- Patient Information -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Patient Information</h5>
                    <p class="mb-1"><strong>Name:</strong> <?php echo e($prescription->patient->full_name); ?></p>
                    <p class="mb-1"><strong>Phone:</strong> <?php echo e($prescription->patient->phone); ?></p>
                    <p class="mb-1"><strong>Date of Birth:</strong> <?php echo e($prescription->patient->date_of_birth->format('M d, Y')); ?></p>
                </div>
                <div class="col-md-6">
                    <h5>Prescription Information</h5>
                    <p class="mb-1"><strong>Prescription Date:</strong> <?php echo e(date('M d, Y', strtotime($prescription->prescription_date))); ?></p>
                    <p class="mb-1"><strong>Created:</strong> <?php echo e($prescription->created_at->format('M d, Y H:i')); ?></p>
                    <p class="mb-1"><strong>Doctor:</strong> Dr. <?php echo e($prescription->doctor->name); ?></p>
                    
                        <p class="mb-1"><strong>Odoo Order Name:</strong> <?php echo e($prescription->odoo_order_name); ?></p>
                        <p class="mb-1"><strong>Odoo Order ID:</strong> <?php echo e($prescription->odoo_order_id); ?></p>
                    
                </div>
            </div>

            <!-- Medications Details -->
            <div class="row mb-4">
                <div class="col-12">
                    <?php if(session('warning')): ?>
                        <div class="alert alert-warning">
                            <?php echo e(session('warning')); ?>

                        </div>
                    <?php endif; ?>
                    
                    <h5>Medications</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <!-- <th>Type</th> -->
                                    <th>Medication</th>
                                    <th>Quantity</th>
                                    <th>Dosage</th>
                                    <th>Schedule</th>
                                    <th>Additional Directions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $prescription->medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <!-- <td>
                                            <span class="badge bg-<?php echo e($medication->type === 'odoo' ? 'primary' : 'info'); ?>">
                                                <?php echo e(ucfirst($medication->type)); ?>

                                            </span>
                                        </td> -->
                                        <td>
                                            <?php if($medication->type === 'odoo'): ?>
                                                <?php echo e($medication->product_name ?? 'Unknown Product'); ?>

                                                <?php if($medication->product_code): ?>
                                                    <br>
                                                    <small class="text-muted">(<?php echo e($medication->product_code); ?>)</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php echo e($medication->product); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($medication->quantity); ?></td>
                                        <td><?php echo e($medication->dosage); ?></td>
                                        <td>
                                            <?php if($medication->every && $medication->period): ?>
                                                Every <?php echo e($medication->every); ?> <?php echo e($medication->period); ?>

                                                <br>
                                            <?php endif; ?>
                                            <?php if($medication->as_needed): ?>
                                                <span class="badge bg-info">As Needed</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($medication->directions); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- ... keep the rest of the file unchanged ... -->

            <!-- Error Information -->
            <?php if($prescription->sync_status === 'error'): ?>
                <div class="alert alert-danger">
                    <h6>Sync Error:</h6>
                    <p class="mb-0"><?php echo e($prescription->sync_error); ?></p>
                </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="mt-4">
                <?php if($prescription->sync_status !== 'synced'): ?>
                    <a href="<?php echo e(route('prescriptions.edit', $prescription)); ?>" class="btn btn-primary">
                        Edit Prescription
                    </a>
                <?php endif; ?>
                
                <?php if(auth()->user()->isAdmin()): ?>
                    <button type="button" class="btn btn-warning" id="resyncButton" 
                            data-prescription-id="<?php echo e($prescription->id); ?>"
                            <?php if($prescription->sync_status === 'synced'): ?> disabled <?php endif; ?>>
                        Resync with Odoo
                    </button>
                <?php endif; ?>

                <a href="<?php echo e(route('prescriptions.index')); ?>" class="btn btn-secondary">
                    Back to List
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#resyncButton').click(function() {
        const button = $(this);
        const prescriptionId = button.data('prescription-id');
        
        button.prop('disabled', true).html('<span class="spinner-border spinner-border-sm"></span> Syncing...');

        $.ajax({
            url: `/prescriptions/${prescriptionId}/resync`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Sync failed: ' + response.message);
                    button.prop('disabled', false).text('Resync with Odoo');
                }
            },
            error: function(xhr) {
                alert('Error occurred during sync. Please try again.');
                button.prop('disabled', false).text('Resync with Odoo');
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\AHCRepos\doctors_portal\resources\views/prescriptions/show.blade.php ENDPATH**/ ?>