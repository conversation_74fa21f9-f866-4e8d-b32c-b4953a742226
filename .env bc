APP_NAME=Laravel
APP_ENV=dev
APP_KEY=base64:wbqj0TF78KDW082pOMHARbHUWOSzlSX6pWxAktvRB98=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=db.braymuprxgppvihegkot.supabase.co
DB_PORT=5432
DB_DATABASE=postgres
DB_USERNAME=postgres
DB_PASSWORD=R<PERSON>an@2024@



BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


ODOO_URL_PROD=https://tadawi.odoo.com
ODOO_DB_PROD=aeltinai-fs-tadawi-main-13942389
ODOO_USERNAME_PROD=<EMAIL>
ODOO_PASSWORD_PROD=Diaa@2024

ODOO_URL_STAGING=https://tadawi.odoo.com
ODOO_DB_STAGING=aeltinai-fs-tadawi-main-13942389
ODOO_USERNAME_STAGING=<EMAIL>
ODOO_PASSWORD_STAGING=Diaa@2024

ODOO_VERIFY_SSL=true
L=aeltinai-fs-tadawi-main-13942389
S=tadawi-staging-15283379


ODOO_URL_STAGING_=https://tadawi.odoo.com
ODOO_DB_STAGING_=tadawi-staging-15283379
ODOO_USERNAME_STAGING_=<EMAIL>
ODOO_PASSWORD_STAGING_=Diaa@2024

